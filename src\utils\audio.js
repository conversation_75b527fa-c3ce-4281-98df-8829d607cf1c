/**
 * Audio Utilities for Neo-Brutalism Portfolio
 * Handles click sounds and audio interactions
 */

class AudioManager {
  constructor() {
    this.sounds = new Map();
    this.isEnabled = true;
    this.volume = 0.3;
    this.preloadSounds();
  }

  preloadSounds() {
    // Preload click sound
    this.loadSound('click', '/spacebar-click-keyboard-199448.mp3');
  }

  loadSound(name, src) {
    try {
      const audio = new Audio(src);
      audio.preload = 'auto';
      audio.volume = this.volume;
      this.sounds.set(name, audio);
    } catch (error) {
      console.warn(`Failed to load sound: ${name}`, error);
    }
  }

  playSound(name) {
    if (!this.isEnabled) return;

    try {
      const sound = this.sounds.get(name);
      if (sound) {
        // Clone the audio to allow multiple simultaneous plays
        const audioClone = sound.cloneNode();
        audioClone.volume = this.volume;
        audioClone.play().catch(() => {
          // Silently fail if audio can't play (user hasn't interacted yet)
        });
      }
    } catch (error) {
      console.warn(`Failed to play sound: ${name}`, error);
    }
  }

  setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume));
    this.sounds.forEach(sound => {
      sound.volume = this.volume;
    });
  }

  enable() {
    this.isEnabled = true;
  }

  disable() {
    this.isEnabled = false;
  }

  toggle() {
    this.isEnabled = !this.isEnabled;
  }
}

// Create global audio manager instance
const audioManager = new AudioManager();

// Export convenience functions
export const playClickSound = () => audioManager.playSound('click');
export const setAudioVolume = (volume) => audioManager.setVolume(volume);
export const enableAudio = () => audioManager.enable();
export const disableAudio = () => audioManager.disable();
export const toggleAudio = () => audioManager.toggle();

export default audioManager;
