/**
 * Simple Browser Diagnostic Script
 * Uses <PERSON><PERSON> to capture console errors quickly
 */

import { chromium } from 'playwright';

async function simpleDiagnose() {
  console.log('🔍 Quick diagnostic check...');
  
  const browser = await chromium.launch({ headless: true });
  const page = await browser.newPage();
  
  const errors = [];
  const warnings = [];
  
  page.on('console', msg => {
    if (msg.type() === 'error') {
      errors.push(msg.text());
      console.log(`❌ ERROR: ${msg.text()}`);
    } else if (msg.type() === 'warning') {
      warnings.push(msg.text());
      console.log(`⚠️  WARNING: ${msg.text()}`);
    }
  });
  
  page.on('pageerror', error => {
    errors.push(error.message);
    console.log(`💥 JS ERROR: ${error.message}`);
  });
  
  try {
    await page.goto('http://localhost:3000', { 
      waitUntil: 'networkidle',
      timeout: 15000 
    });
    
    // Wait a bit for React to load
    await page.waitForTimeout(3000);
    
    // Check if React app mounted
    const hasContent = await page.evaluate(() => {
      const root = document.getElementById('root');
      return root && root.children.length > 0;
    });
    
    console.log(`\n📊 Results:`);
    console.log(`React app mounted: ${hasContent ? '✅' : '❌'}`);
    console.log(`Errors: ${errors.length}`);
    console.log(`Warnings: ${warnings.length}`);
    
    if (errors.length === 0 && hasContent) {
      console.log('🎉 Site appears to be loading correctly!');
    }
    
  } catch (error) {
    console.error('❌ Failed to load page:', error.message);
  } finally {
    await browser.close();
  }
}

simpleDiagnose();
