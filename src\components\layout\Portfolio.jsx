import { useRef } from 'react';
import { gsap, useGSAP } from '@/utils/gsap';
import PlaceholderImage from '@/components/ui/PlaceholderImage';
import './Portfolio.css';

const Portfolio = () => {
  const sectionRef = useRef();
  const titleRef = useRef();
  const portfolioRef = useRef();

  const projects = [
    {
      title: 'CHALAKA DULANGA PHOTOGRAPHY',
      category: 'Photography Portfolio',
      description: 'Premium photography portfolio showcasing fine art wedding photography with elegant design and smooth user experience.',
      image: '/images/portfolio/chalaka-dulanga-photography-thumb.png',
      tags: ['React', 'GSAP', 'Photography', 'Portfolio'],
      color: 'var(--color-neon-green)',
      link: 'https://chalakadulangaphotography.com'
    },
    {
      title: 'ZEYNTHRA',
      category: 'Music Artist',
      description: 'Progressive music DJ website with dynamic audio integration and immersive visual experience.',
      image: '/images/portfolio/zeynthra-thumb.png',
      tags: ['Next.js', 'Audio', 'Music', 'Interactive'],
      color: 'var(--color-hot-pink)',
      link: 'https://zeynthra.com'
    },
    {
      title: 'SPICE & HERB RESTAURANT',
      category: 'Restaurant Website',
      description: 'Authentic Sri Lankan cuisine restaurant website with online ordering and reservation system.',
      image: '/images/portfolio/spice-herb-restaurant-thumb.png',
      tags: ['Restaurant', 'E-commerce', 'Booking', 'Food'],
      color: 'var(--color-cyber-yellow)',
      link: 'https://spiceandherbrestaurant.com'
    },
  ];

  useGSAP(() => {
    // Title animation
    gsap.fromTo(titleRef.current, 
      { opacity: 0, y: 60 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      }
    );

    // Portfolio cards animation
    const cards = portfolioRef.current.querySelectorAll('.portfolio-card');
    gsap.fromTo(cards,
      { opacity: 0, y: 100, rotationX: 45 },
      {
        opacity: 1,
        y: 0,
        rotationX: 0,
        duration: 1,
        ease: 'power3.out',
        stagger: 0.2,
        scrollTrigger: {
          trigger: portfolioRef.current,
          start: 'top 70%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      }
    );

  }, { scope: sectionRef });

  return (
    <section ref={sectionRef} id="work" className="portfolio">
      <div className="container">
        <div className="portfolio-header">
          <h2 ref={titleRef} className="section-title">
            <span className="title-label">WORK THAT</span>
            <span className="title-main">WORKS</span>
          </h2>
          <p className="section-subtitle">
            Ready to make your next project a success?
          </p>
        </div>
        
        <div ref={portfolioRef} className="portfolio-grid">
          {projects.map((project, index) => (
            <div key={index} className="portfolio-card" style={{ '--accent-color': project.color }}>
              <div className="portfolio-image">
                <img
                  src={project.image}
                  alt={project.title}
                  loading="lazy"
                />
                <div className="portfolio-overlay">
                  <a href={project.link} target="_blank" rel="noopener noreferrer" className="portfolio-link">
                    VIEW SITE
                  </a>
                </div>
              </div>
              
              <div className="portfolio-content">
                <div className="portfolio-meta">
                  <span className="portfolio-category">{project.category}</span>
                  <div className="portfolio-accent" style={{ backgroundColor: project.color }}></div>
                </div>
                
                <h3 className="portfolio-title">{project.title}</h3>
                <p className="portfolio-description">{project.description}</p>
                
                <div className="portfolio-tags">
                  {project.tags.map((tag, tagIndex) => (
                    <span key={tagIndex} className="portfolio-tag">
                      {tag}
                    </span>
                  ))}
                </div>
                
                <a href={project.link} target="_blank" rel="noopener noreferrer" className="portfolio-btn">
                  EXPLORE PROJECT
                </a>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
