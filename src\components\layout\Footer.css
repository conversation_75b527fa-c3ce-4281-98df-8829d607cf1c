/**
 * Footer Component Styles
 * Neo-Brutalism footer with proper spacing
 */

.footer {
  background-color: var(--color-primary);
  color: var(--color-secondary);
  padding: var(--space-20) 0 var(--space-8);
  border-top: 3px solid var(--color-accent);
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: repeating-linear-gradient(
    90deg,
    var(--color-accent) 0px,
    var(--color-accent) 20px,
    var(--color-neon-green) 20px,
    var(--color-neon-green) 40px,
    var(--color-cyber-yellow) 40px,
    var(--color-cyber-yellow) 60px,
    var(--color-hot-pink) 60px,
    var(--color-hot-pink) 80px
  );
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  margin-bottom: var(--space-16);
}

.footer-main {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.footer-logo {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.logo-text {
  font-family: var(--font-display);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-black);
  color: var(--color-secondary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-tight);
}

.logo-tagline {
  font-family: var(--font-display);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-300);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

.footer-description {
  max-width: 400px;
}

.footer-description p {
  font-size: var(--font-size-base);
  color: var(--color-gray-300);
  line-height: var(--line-height-relaxed);
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-8);
}

.footer-column h4 {
  font-family: var(--font-display);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-secondary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  margin-bottom: var(--space-6);
  position: relative;
}

.footer-column h4::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 30px;
  height: 3px;
  background-color: var(--color-accent);
}

.footer-column ul {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.footer-column a {
  font-size: var(--font-size-base);
  color: var(--color-gray-300);
  text-decoration: none;
  transition: var(--transition-fast);
  position: relative;
}

.footer-column a::before {
  content: '→';
  position: absolute;
  left: -20px;
  opacity: 0;
  transition: var(--transition-fast);
  color: var(--color-accent);
}

.footer-column a:hover {
  color: var(--color-secondary);
  transform: translateX(10px);
}

.footer-column a:hover::before {
  opacity: 1;
}

.footer-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: var(--space-8);
  border-top: 1px solid var(--color-gray-700);
}

.footer-copyright p {
  font-size: var(--font-size-sm);
  color: var(--color-gray-400);
}

.footer-legal {
  display: flex;
  gap: var(--space-6);
}

.footer-legal a {
  font-size: var(--font-size-sm);
  color: var(--color-gray-400);
  text-decoration: none;
  transition: var(--transition-fast);
}

.footer-legal a:hover {
  color: var(--color-secondary);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }
  
  .footer-links {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-6);
  }
}

@media (max-width: 768px) {
  .footer {
    padding: var(--space-16) 0 var(--space-6);
  }
  
  .footer-content {
    gap: var(--space-10);
    margin-bottom: var(--space-12);
  }
  
  .footer-links {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }
  
  .footer-legal {
    gap: var(--space-4);
  }
}

@media (max-width: 480px) {
  .footer {
    padding: var(--space-12) 0 var(--space-5);
  }
  
  .logo-text {
    font-size: var(--font-size-2xl);
  }
  
  .footer-main {
    gap: var(--space-6);
  }
  
  .footer-links {
    gap: var(--space-6);
  }
  
  .footer-column h4 {
    font-size: var(--font-size-base);
    margin-bottom: var(--space-4);
  }
  
  .footer-legal {
    flex-direction: column;
    gap: var(--space-2);
  }
}
