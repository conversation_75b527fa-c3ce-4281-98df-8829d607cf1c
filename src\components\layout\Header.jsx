import { useRef } from 'react';
import { gsap, useGSAP } from '@/utils/gsap';
import BrutalButton from '@/components/ui/BrutalButton';
import ScrollReveal from '@/components/animations/ScrollReveal';
import './Header.css';

const Header = () => {
  const headerRef = useRef();
  const logoRef = useRef();
  const navRef = useRef();

  useGSAP(() => {
    // Initial setup
    gsap.set([logoRef.current, navRef.current], {
      opacity: 0,
      y: -20,
    });

    // Entrance animation
    const tl = gsap.timeline();
    tl.to(logoRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.6,
      ease: 'power2.out',
    })
    .to(navRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.6,
      ease: 'power2.out',
    }, '-=0.3');

  }, { scope: headerRef });

  return (
    <header ref={headerRef} className="header">
      <div className="container">
        <div className="header-content">
          <div ref={logoRef} className="logo">
            <span className="logo-text">TERA</span>
            <span className="logo-dot"></span>
          </div>
          
          <nav ref={navRef} className="nav">
            <a href="#services" className="nav-link">SERVICES</a>
            <a href="#work" className="nav-link">WORK</a>
            <a href="#about" className="nav-link">ABOUT</a>
            <BrutalButton
              href="#contact"
              variant="primary"
              size="sm"
              className="nav-cta"
            >
              GET IN TOUCH
            </BrutalButton>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;
