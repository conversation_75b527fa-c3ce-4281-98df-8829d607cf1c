import { useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { gsap, useGSAP } from '@/utils/gsap';
import './BrutalButton.css';

const BrutalButton = ({ 
  children, 
  variant = 'primary', 
  size = 'md', 
  onClick, 
  href,
  className = '',
  disabled = false,
  icon,
  ...props 
}) => {
  const buttonRef = useRef();
  const shadowRef = useRef();
  const [isPressed, setIsPressed] = useState(false);

  // Audio context for click sounds
  const playClickSound = () => {
    try {
      const audio = new Audio('/spacebar-click-keyboard-199448.mp3');
      audio.volume = 0.3;
      audio.play().catch(() => {
        // Silently fail if audio can't play
      });
    } catch (error) {
      // Silently fail if audio file not found
    }
  };

  useGSAP(() => {
    const button = buttonRef.current;
    const shadow = shadowRef.current;

    // Hover animation
    const handleMouseEnter = () => {
      gsap.to(button, {
        x: -2,
        y: -2,
        duration: 0.2,
        ease: 'power2.out',
      });
      gsap.to(shadow, {
        x: 2,
        y: 2,
        duration: 0.2,
        ease: 'power2.out',
      });
    };

    const handleMouseLeave = () => {
      gsap.to(button, {
        x: 0,
        y: 0,
        duration: 0.3,
        ease: 'elastic.out(1, 0.5)',
      });
      gsap.to(shadow, {
        x: 0,
        y: 0,
        duration: 0.3,
        ease: 'elastic.out(1, 0.5)',
      });
    };

    const handleMouseDown = () => {
      setIsPressed(true);
      gsap.to(button, {
        x: 1,
        y: 1,
        scale: 0.98,
        duration: 0.1,
        ease: 'power2.out',
      });
      gsap.to(shadow, {
        x: -1,
        y: -1,
        scale: 0.95,
        duration: 0.1,
        ease: 'power2.out',
      });
    };

    const handleMouseUp = () => {
      setIsPressed(false);
      gsap.to(button, {
        x: -2,
        y: -2,
        scale: 1,
        duration: 0.2,
        ease: 'back.out(1.7)',
      });
      gsap.to(shadow, {
        x: 2,
        y: 2,
        scale: 1,
        duration: 0.2,
        ease: 'back.out(1.7)',
      });
    };

    if (button && !disabled) {
      button.addEventListener('mouseenter', handleMouseEnter);
      button.addEventListener('mouseleave', handleMouseLeave);
      button.addEventListener('mousedown', handleMouseDown);
      button.addEventListener('mouseup', handleMouseUp);

      return () => {
        button.removeEventListener('mouseenter', handleMouseEnter);
        button.removeEventListener('mouseleave', handleMouseLeave);
        button.removeEventListener('mousedown', handleMouseDown);
        button.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, { scope: buttonRef });

  const handleClick = (e) => {
    playClickSound();
    if (onClick) {
      onClick(e);
    }
  };

  const buttonClasses = `
    brutal-button 
    brutal-button--${variant} 
    brutal-button--${size}
    ${disabled ? 'brutal-button--disabled' : ''}
    ${className}
  `.trim();

  const buttonContent = (
    <div className="brutal-button-container">
      <div ref={shadowRef} className="brutal-button-shadow"></div>
      <motion.button
        ref={buttonRef}
        className={buttonClasses}
        onClick={handleClick}
        disabled={disabled}
        whileTap={{ scale: 0.95 }}
        {...props}
      >
        <span className="brutal-button-content">
          {icon && <span className="brutal-button-icon">{icon}</span>}
          <span className="brutal-button-text">{children}</span>
        </span>
        <div className="brutal-button-glitch"></div>
      </motion.button>
    </div>
  );

  if (href && !disabled) {
    return (
      <a href={href} className="brutal-button-link">
        {buttonContent}
      </a>
    );
  }

  return buttonContent;
};

export default BrutalButton;
