import { useRef } from 'react';
import { gsap, useGSAP } from '@/utils/gsap';
import './Contact.css';

const Contact = () => {
  const sectionRef = useRef();
  const titleRef = useRef();
  const formRef = useRef();
  const infoRef = useRef();

  useGSAP(() => {
    // Title animation
    gsap.fromTo(titleRef.current, 
      { opacity: 0, y: 60 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      }
    );

    // Form animation
    gsap.fromTo(formRef.current, 
      { opacity: 0, x: -60 },
      {
        opacity: 1,
        x: 0,
        duration: 0.8,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: formRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      }
    );

    // Info animation
    gsap.fromTo(infoRef.current, 
      { opacity: 0, x: 60 },
      {
        opacity: 1,
        x: 0,
        duration: 0.8,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: infoRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      }
    );

  }, { scope: sectionRef });

  return (
    <section ref={sectionRef} id="contact" className="contact">
      <div className="container">
        <div className="contact-header">
          <h2 ref={titleRef} className="section-title">
            <span className="title-label">LET'S</span>
            <span className="title-main">TALK</span>
          </h2>
        </div>
        
        <div className="contact-content">
          <div ref={formRef} className="contact-form-wrapper">
            <div className="contact-form-header">
              <h3>GET IN TOUCH</h3>
              <p>Ready to start your next project? Let's discuss your ideas and bring them to life.</p>
            </div>
            
            <form className="contact-form">
              <div className="form-group">
                <label htmlFor="name">NAME *</label>
                <input type="text" id="name" name="name" required />
              </div>
              
              <div className="form-group">
                <label htmlFor="email">EMAIL *</label>
                <input type="email" id="email" name="email" required />
              </div>
              
              <div className="form-group">
                <label htmlFor="project">PROJECT TYPE</label>
                <select id="project" name="project">
                  <option value="">Select a service</option>
                  <option value="web-development">Web Development</option>
                  <option value="mobile-app">Mobile App</option>
                  <option value="digital-marketing">Digital Marketing</option>
                  <option value="branding">Branding</option>
                  <option value="other">Other</option>
                </select>
              </div>
              
              <div className="form-group">
                <label htmlFor="budget">BUDGET RANGE</label>
                <select id="budget" name="budget">
                  <option value="">Select budget range</option>
                  <option value="5k-10k">$5,000 - $10,000</option>
                  <option value="10k-25k">$10,000 - $25,000</option>
                  <option value="25k-50k">$25,000 - $50,000</option>
                  <option value="50k+">$50,000+</option>
                </select>
              </div>
              
              <div className="form-group">
                <label htmlFor="message">MESSAGE *</label>
                <textarea id="message" name="message" rows="5" required placeholder="Tell us about your project..."></textarea>
              </div>
              
              <button type="submit" className="brutal-btn brutal-btn-primary brutal-btn-lg contact-submit">
                SEND MESSAGE
              </button>
            </form>
          </div>
          
          <div ref={infoRef} className="contact-info">
            <div className="contact-card">
              <h4>QUICK RESPONSE GUARANTEE</h4>
              <p>We respond to all inquiries within 24 hours. Usually much faster!</p>
            </div>
            
            <div className="contact-details">
              <div className="contact-item">
                <h5>EMAIL</h5>
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </div>
              
              <div className="contact-item">
                <h5>PHONE</h5>
                <a href="tel:+94123456789">+94 12 345 6789</a>
              </div>
              
              <div className="contact-item">
                <h5>LOCATION</h5>
                <p>Remote & Global<br />Available Worldwide</p>
              </div>
            </div>
            
            <div className="contact-social">
              <h5>FOLLOW US</h5>
              <div className="social-links">
                <a href="#" className="social-link">LINKEDIN</a>
                <a href="#" className="social-link">TWITTER</a>
                <a href="#" className="social-link">INSTAGRAM</a>
                <a href="#" className="social-link">GITHUB</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
