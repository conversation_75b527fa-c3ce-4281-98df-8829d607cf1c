import { useRef, useState } from 'react';
import { gsap, useGSAP } from '@/utils/gsap';
import BrutalCard from '@/components/ui/BrutalCard';
import BrutalButton from '@/components/ui/BrutalButton';
import ScrollReveal from '@/components/animations/ScrollReveal';
import { sendToWhatsApp, validateFormData, getWhatsAppInfo } from '@/utils/whatsapp';
import './Contact.css';

const Contact = () => {
  const sectionRef = useRef();
  const titleRef = useRef();
  const formRef = useRef();
  const infoRef = useRef();

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    project: '',
    budget: '',
    message: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const whatsappInfo = getWhatsAppInfo();

  // Form handlers
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);

    // Validate form
    const validation = validateFormData(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      setIsSubmitting(false);
      return;
    }

    try {
      // Send to WhatsApp
      sendToWhatsApp(formData);
      setSubmitStatus('success');
      setFormData({
        name: '',
        email: '',
        project: '',
        budget: '',
        message: ''
      });
    } catch {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  useGSAP(() => {
    // Title animation
    gsap.fromTo(titleRef.current, 
      { opacity: 0, y: 60 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      }
    );

    // Form animation
    gsap.fromTo(formRef.current, 
      { opacity: 0, x: -60 },
      {
        opacity: 1,
        x: 0,
        duration: 0.8,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: formRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      }
    );

    // Info animation
    gsap.fromTo(infoRef.current, 
      { opacity: 0, x: 60 },
      {
        opacity: 1,
        x: 0,
        duration: 0.8,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: infoRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      }
    );

  }, { scope: sectionRef });

  return (
    <section ref={sectionRef} id="contact" className="contact">
      <div className="container">
        <div className="contact-header">
          <ScrollReveal animation="fadeUp" delay={0.2}>
            <h2 ref={titleRef} className="section-title">
              <span className="title-label">LET'S</span>
              <span className="title-main">TALK</span>
            </h2>
          </ScrollReveal>
        </div>
        
        <div className="contact-content">
          <ScrollReveal animation="fadeLeft" className="contact-form-wrapper">
            <BrutalCard
              variant="secondary"
              accentColor="var(--color-accent)"
              className="contact-form-card"
            >
              <div className="contact-form-header">
                <h3>GET IN TOUCH</h3>
                <p>Ready to start your next project? Let's discuss your ideas and bring them to life.</p>
                {submitStatus === 'success' && (
                  <div className="success-message">
                    ✅ Message sent successfully! We'll redirect you to WhatsApp.
                  </div>
                )}
                {submitStatus === 'error' && (
                  <div className="error-message">
                    ❌ Something went wrong. Please try again.
                  </div>
                )}
              </div>

              <form className="contact-form" onSubmit={handleSubmit}>
                <div className="form-group">
                  <label htmlFor="name">NAME *</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className={errors.name ? 'error' : ''}
                    required
                  />
                  {errors.name && <span className="field-error">{errors.name}</span>}
                </div>

                <div className="form-group">
                  <label htmlFor="email">EMAIL *</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={errors.email ? 'error' : ''}
                    required
                  />
                  {errors.email && <span className="field-error">{errors.email}</span>}
                </div>

                <div className="form-group">
                  <label htmlFor="project">PROJECT TYPE</label>
                  <select
                    id="project"
                    name="project"
                    value={formData.project}
                    onChange={handleInputChange}
                  >
                    <option value="">Select a service</option>
                    <option value="web-development">Web Development</option>
                    <option value="mobile-app">Mobile App</option>
                    <option value="digital-marketing">Digital Marketing</option>
                    <option value="branding">Branding</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div className="form-group">
                  <label htmlFor="budget">BUDGET RANGE</label>
                  <select
                    id="budget"
                    name="budget"
                    value={formData.budget}
                    onChange={handleInputChange}
                  >
                    <option value="">Select budget range</option>
                    <option value="5k-10k">$5,000 - $10,000</option>
                    <option value="10k-25k">$10,000 - $25,000</option>
                    <option value="25k-50k">$25,000 - $50,000</option>
                    <option value="50k+">$50,000+</option>
                  </select>
                </div>

                <div className="form-group">
                  <label htmlFor="message">MESSAGE *</label>
                  <textarea
                    id="message"
                    name="message"
                    rows="5"
                    value={formData.message}
                    onChange={handleInputChange}
                    className={errors.message ? 'error' : ''}
                    placeholder="Tell us about your project..."
                    required
                  />
                  {errors.message && <span className="field-error">{errors.message}</span>}
                </div>

                <BrutalButton
                  type="submit"
                  variant="primary"
                  size="lg"
                  disabled={isSubmitting}
                  className="contact-submit"
                  icon="📱"
                >
                  {isSubmitting ? 'SENDING...' : 'SEND VIA WHATSAPP'}
                </BrutalButton>
              </form>
            </BrutalCard>
          </ScrollReveal>
          
          <ScrollReveal animation="fadeRight" className="contact-info">
            <BrutalCard
              variant="primary"
              accentColor="var(--color-neon-green)"
              glow={true}
              className="contact-card"
            >
              <h4>QUICK RESPONSE GUARANTEE</h4>
              <p>We respond to all inquiries within {whatsappInfo.responseTime}!</p>
              <p><strong>Business Hours:</strong> {whatsappInfo.businessHours}</p>
              <p><strong>Availability:</strong> {whatsappInfo.availability}</p>
            </BrutalCard>

            <div className="contact-details">
              <BrutalCard variant="secondary" className="contact-item">
                <h5>EMAIL</h5>
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </BrutalCard>

              <BrutalCard variant="secondary" className="contact-item">
                <h5>WHATSAPP</h5>
                <a href={`https://wa.me/${whatsappInfo.number.replace('+', '')}`} target="_blank" rel="noopener noreferrer">
                  {whatsappInfo.displayNumber}
                </a>
              </BrutalCard>

              <BrutalCard variant="secondary" className="contact-item">
                <h5>LOCATION</h5>
                <p>Remote & Global<br />Available Worldwide</p>
              </BrutalCard>
            </div>

            <BrutalCard
              variant="dark"
              accentColor="var(--color-hot-pink)"
              className="contact-social"
            >
              <h5>FOLLOW US</h5>
              <div className="social-links">
                <BrutalButton variant="outline" size="sm" href="#" className="social-link">
                  LINKEDIN
                </BrutalButton>
                <BrutalButton variant="outline" size="sm" href="#" className="social-link">
                  TWITTER
                </BrutalButton>
                <BrutalButton variant="outline" size="sm" href="#" className="social-link">
                  INSTAGRAM
                </BrutalButton>
                <BrutalButton variant="outline" size="sm" href="#" className="social-link">
                  GITHUB
                </BrutalButton>
              </div>
            </BrutalCard>
          </ScrollReveal>
        </div>
      </div>
    </section>
  );
};

export default Contact;
