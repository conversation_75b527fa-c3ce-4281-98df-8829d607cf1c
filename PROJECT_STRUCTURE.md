# Tera Works Portfolio - Project Structure

## 📁 Directory Structure

```
tera-works-portfolio/
├── public/                     # Static assets
│   └── vite.svg               # Vite logo
├── src/                       # Source code
│   ├── components/            # React components
│   │   ├── layout/           # Layout components (<PERSON><PERSON>, Footer, etc.)
│   │   ├── ui/               # Reusable UI components (<PERSON><PERSON>, Card, etc.)
│   │   ├── portfolio/        # Portfolio-specific components
│   │   └── animations/       # GSAP animation components
│   ├── styles/               # CSS and styling
│   │   ├── reset.css         # Modern CSS reset + utilities
│   │   └── theme.css         # Neo-Brutalism theme system
│   ├── assets/               # Images, fonts, static assets
│   │   ├── images/           # Image files
│   │   └── fonts/            # Custom fonts
│   ├── hooks/                # Custom React hooks
│   ├── utils/                # Utility functions
│   │   └── gsap.js           # GSAP configuration and utilities
│   ├── data/                 # Static data and content
│   ├── App.jsx               # Main App component
│   ├── App.css               # App-specific styles
│   ├── main.jsx              # React entry point
│   └── index.css             # Global styles
├── eslint.config.js          # ESLint configuration
├── vite.config.js            # Vite configuration
├── package.json              # Dependencies and scripts
├── README.md                 # Project documentation
└── PROJECT_STRUCTURE.md      # This file
```

## 🛠️ Configuration Files

### package.json
- **Dependencies**: React 19, GSAP, @gsap/react
- **Dev Dependencies**: Vite, ESLint, TypeScript types
- **Scripts**: dev, build, lint, preview, clean

### vite.config.js
- **Aliases**: Path aliases for clean imports (@, @components, etc.)
- **Build Optimization**: Code splitting for vendor and GSAP chunks
- **Dev Server**: Port 3000 with auto-open

### eslint.config.js
- **Modern ESLint**: Flat config with React hooks support
- **Quality Rules**: Strict linting for code consistency
- **React Refresh**: Hot reload support

## 🎨 Design System

### CSS Architecture
- **reset.css**: Modern CSS reset with accessibility features
- **theme.css**: Complete Neo-Brutalism design system
- **index.css**: Global styles and imports

### Design Tokens
- **Colors**: Primary, secondary, accent, and semantic colors
- **Typography**: Font scales, weights, and line heights
- **Spacing**: Consistent spacing scale
- **Shadows**: Brutalist shadow system
- **Breakpoints**: Responsive design breakpoints

### Typography System
- **Font Family**: Inter for clean, modern typography
- **Display Classes**: .text-display-1 through .text-display-3
- **Heading Classes**: .text-h1 through .text-h6
- **Body Classes**: .text-body-lg, .text-body, .text-body-sm
- **Utility Classes**: .text-caption, color utilities

## 🎯 GSAP Integration

### utils/gsap.js
- **Plugin Registration**: ScrollTrigger, useGSAP hook
- **Animation Presets**: Common Neo-Brutalism animations
- **Utility Functions**: Scroll animations, cleanup helpers
- **Default Settings**: Consistent easing and duration

### Animation System
- **Entrance Animations**: fadeInUp, fadeInLeft, scaleIn, rotateIn
- **Scroll Triggers**: Consistent scroll-based animations
- **Stagger Effects**: Coordinated multi-element animations
- **Performance**: Optimized with force3D and proper cleanup

## 🚀 Development Workflow

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Check code quality
npm run lint:fix     # Fix linting issues
npm run clean        # Clean build directory
```

### Code Quality
- **ESLint**: Strict linting rules for consistency
- **Modern JavaScript**: ES6+ features and best practices
- **React Best Practices**: Hooks, functional components
- **Accessibility**: WCAG compliant design patterns

## 📱 Responsive Design

### Breakpoints
- **Mobile**: < 640px
- **Tablet**: 640px - 1024px
- **Desktop**: 1024px - 1280px
- **Large Desktop**: > 1280px

### Mobile-First Approach
- Base styles for mobile
- Progressive enhancement for larger screens
- Touch-friendly interactions
- Optimized typography scaling

## 🎨 Neo-Brutalism Features

### Design Principles
- **Bold Typography**: Large, impactful text as design elements
- **High Contrast**: Strong color contrasts for visual impact
- **Geometric Shapes**: Unconventional layouts and forms
- **Raw Aesthetics**: Honest, unpolished digital materials
- **Strategic Shadows**: 3D effects with brutal shadow system
- **Asymmetric Layouts**: Intentionally unbalanced compositions

### Implementation
- **CSS Custom Properties**: Consistent design tokens
- **Utility Classes**: Reusable styling components
- **Component Architecture**: Modular, maintainable code
- **Animation Integration**: Smooth GSAP-powered interactions

## 🎯 Completed Features

### Layout Components
- **Header**: Fixed navigation with Neo-Brutalism styling
- **Hero**: Animated hero section with GSAP entrance effects
- **Services**: 6-card service grid with hover animations
- **Portfolio**: 3-project showcase with placeholder images
- **Contact**: Contact form with info cards and social links
- **Footer**: Multi-column footer with animated links

### Design System
- **Complete CSS Architecture**: Reset, theme, and component styles
- **Typography System**: 9 display sizes + body text classes
- **Color Palette**: Primary, secondary, accent, and semantic colors
- **Spacing System**: 32-step consistent spacing scale
- **Shadow System**: 5 levels of brutal shadow effects
- **Animation Presets**: GSAP-powered entrance and scroll animations

### Technical Features
- **GSAP Integration**: ScrollTrigger animations throughout
- **Responsive Design**: Mobile-first approach with breakpoints
- **Accessibility**: WCAG compliant focus states and semantics
- **Performance**: Optimized with code splitting and lazy loading
- **Code Quality**: ESLint passing with strict rules

---

**Status**: ✅ Neo-Brutalism Design System Implementation Complete
**Next Phase**: Portfolio Content Capture & Optimization
