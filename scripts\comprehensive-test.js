import { chromium } from 'playwright';

async function comprehensiveTest() {
  console.log('🚀 Running comprehensive site test...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Listen for all console messages
  const logs = [];
  page.on('console', msg => {
    logs.push({ type: msg.type(), text: msg.text() });
    if (msg.type() === 'error') {
      console.log('❌ Error:', msg.text());
    } else if (msg.type() === 'warning') {
      console.log('⚠️ Warning:', msg.text());
    }
  });
  
  // Listen for page errors
  page.on('pageerror', error => {
    console.log('❌ Page Error:', error.message);
  });
  
  try {
    console.log('📱 Loading the portfolio site...');
    await page.goto('http://localhost:3000/', { waitUntil: 'networkidle' });
    
    // Wait for initial load
    await page.waitForTimeout(2000);
    
    console.log('🔍 Testing core components...');
    
    // Test Hero section
    const heroSection = await page.$('.hero-section, [data-testid="hero"]');
    console.log(heroSection ? '✅ Hero section found' : '❌ Hero section missing');
    
    // Test Navigation
    const nav = await page.$('nav, .navigation, .navbar');
    console.log(nav ? '✅ Navigation found' : '❌ Navigation missing');
    
    // Test BrutalButton components
    const brutalButtons = await page.$$('.brutal-button');
    console.log(`✅ Found ${brutalButtons.length} BrutalButton components`);
    
    // Test BrutalCard components
    const brutalCards = await page.$$('.brutal-card');
    console.log(`✅ Found ${brutalCards.length} BrutalCard components`);
    
    // Test ScrollReveal animations
    const scrollRevealElements = await page.$$('.scroll-reveal');
    console.log(`✅ Found ${scrollRevealElements.length} ScrollReveal elements`);
    
    // Test click sounds (check if audio files are loaded)
    console.log('🔊 Testing click sound functionality...');
    if (brutalButtons.length > 0) {
      try {
        await brutalButtons[0].click();
        await page.waitForTimeout(500);
        console.log('✅ Button click test completed');
      } catch (error) {
        console.log('⚠️ Button click test failed:', error.message);
      }
    }
    
    // Test GSAP animations
    console.log('🎬 Testing GSAP animations...');
    const gsapElements = await page.$$('[data-gsap], .gsap-animated');
    console.log(`✅ Found ${gsapElements.length} GSAP animated elements`);
    
    // Test scroll behavior
    console.log('📜 Testing scroll animations...');
    await page.evaluate(() => window.scrollTo(0, 500));
    await page.waitForTimeout(1000);
    await page.evaluate(() => window.scrollTo(0, 1000));
    await page.waitForTimeout(1000);
    await page.evaluate(() => window.scrollTo(0, 0));
    await page.waitForTimeout(1000);
    
    // Check for WhatsApp integration
    const whatsappLinks = await page.$$('a[href*="wa.me"], a[href*="whatsapp"]');
    console.log(`✅ Found ${whatsappLinks.length} WhatsApp integration links`);
    
    // Test contact form if present
    const contactForm = await page.$('form, .contact-form');
    console.log(contactForm ? '✅ Contact form found' : 'ℹ️ No contact form detected');
    
    // Check for portfolio images
    const portfolioImages = await page.$$('img[src*="portfolio"], .portfolio img');
    console.log(`✅ Found ${portfolioImages.length} portfolio images`);
    
    // Test responsive design
    console.log('📱 Testing responsive design...');
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(1000);
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(1000);
    
    // Final screenshot
    await page.screenshot({ path: 'comprehensive-test.png', fullPage: true });
    console.log('📸 Final screenshot saved as comprehensive-test.png');
    
    // Analyze console logs
    const errors = logs.filter(log => log.type === 'error');
    const warnings = logs.filter(log => log.type === 'warning');
    
    console.log('\n📊 Comprehensive Test Results:');
    console.log('================================');
    console.log(`✅ Total console errors: ${errors.length}`);
    console.log(`⚠️ Total warnings: ${warnings.length}`);
    console.log(`🎯 BrutalButton components: ${brutalButtons.length}`);
    console.log(`🎯 BrutalCard components: ${brutalCards.length}`);
    console.log(`🎬 ScrollReveal elements: ${scrollRevealElements.length}`);
    console.log(`🎭 GSAP elements: ${gsapElements.length}`);
    console.log(`📱 WhatsApp links: ${whatsappLinks.length}`);
    console.log(`🖼️ Portfolio images: ${portfolioImages.length}`);
    
    if (errors.length === 0) {
      console.log('\n🎉 ALL TESTS PASSED! Site is working perfectly!');
    } else {
      console.log('\n⚠️ Some issues detected. Check errors above.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await browser.close();
  }
}

comprehensiveTest().catch(console.error);
