/**
 * Diagnose Loading Issues Script
 * Uses Playwright to capture console errors and network failures
 */

import { chromium } from 'playwright';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function diagnoseLoadingIssues() {
  console.log('🔍 Starting comprehensive error diagnosis...');
  
  const browser = await chromium.launch({ 
    headless: false,
    devtools: true 
  });
  
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 }
  });
  
  const page = await context.newPage();
  
  // Capture all console messages
  const consoleMessages = [];
  page.on('console', msg => {
    consoleMessages.push({
      type: msg.type(),
      text: msg.text(),
      location: msg.location()
    });
    console.log(`[${msg.type().toUpperCase()}] ${msg.text()}`);
  });
  
  // Capture network failures
  const networkFailures = [];
  page.on('response', response => {
    if (!response.ok()) {
      networkFailures.push({
        url: response.url(),
        status: response.status(),
        statusText: response.statusText()
      });
      console.log(`[NETWORK ERROR] ${response.status()} ${response.url()}`);
    }
  });
  
  // Capture JavaScript errors
  const jsErrors = [];
  page.on('pageerror', error => {
    jsErrors.push({
      message: error.message,
      stack: error.stack
    });
    console.log(`[JS ERROR] ${error.message}`);
    console.log(`Stack: ${error.stack}`);
  });
  
  try {
    console.log('📍 Navigating to localhost:3000...');
    await page.goto('http://localhost:3000', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    // Wait for potential async loading
    await page.waitForTimeout(5000);
    
    // Check if React app mounted
    const reactRoot = await page.$('#root');
    const hasContent = await page.evaluate(() => {
      const root = document.getElementById('root');
      return root && root.children.length > 0;
    });
    
    console.log(`React root found: ${!!reactRoot}`);
    console.log(`Has content: ${hasContent}`);
    
    // Check for specific components
    const components = {
      header: await page.$('.header'),
      hero: await page.$('.hero'),
      services: await page.$('.services'),
      portfolio: await page.$('.portfolio'),
      contact: await page.$('.contact'),
      footer: await page.$('.footer')
    };
    
    console.log('\n📊 Component Status:');
    Object.entries(components).forEach(([name, element]) => {
      console.log(`${name}: ${element ? '✅ Found' : '❌ Missing'}`);
    });
    
    // Check for GSAP
    const gsapLoaded = await page.evaluate(() => {
      return typeof window.gsap !== 'undefined';
    });
    console.log(`GSAP loaded: ${gsapLoaded ? '✅' : '❌'}`);
    
    // Check for Framer Motion
    const framerMotionLoaded = await page.evaluate(() => {
      return typeof window.FramerMotion !== 'undefined' || 
             document.querySelector('[data-framer-motion]') !== null;
    });
    console.log(`Framer Motion components: ${framerMotionLoaded ? '✅' : '❌'}`);
    
    // Summary
    console.log('\n📋 Error Summary:');
    console.log(`Console Messages: ${consoleMessages.length}`);
    console.log(`Network Failures: ${networkFailures.length}`);
    console.log(`JavaScript Errors: ${jsErrors.length}`);
    
    if (consoleMessages.length > 0) {
      console.log('\n🔍 Console Messages:');
      consoleMessages.forEach((msg, i) => {
        console.log(`${i + 1}. [${msg.type}] ${msg.text}`);
        if (msg.location) {
          console.log(`   Location: ${msg.location.url}:${msg.location.lineNumber}`);
        }
      });
    }
    
    if (networkFailures.length > 0) {
      console.log('\n🌐 Network Failures:');
      networkFailures.forEach((failure, i) => {
        console.log(`${i + 1}. ${failure.status} ${failure.url}`);
      });
    }
    
    if (jsErrors.length > 0) {
      console.log('\n💥 JavaScript Errors:');
      jsErrors.forEach((error, i) => {
        console.log(`${i + 1}. ${error.message}`);
        if (error.stack) {
          console.log(`   Stack: ${error.stack.split('\n')[0]}`);
        }
      });
    }
    
    // Keep browser open for manual inspection
    console.log('\n🔍 Browser kept open for manual inspection...');
    console.log('Press Ctrl+C to close when done inspecting.');
    
    // Wait indefinitely until user closes
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Diagnosis failed:', error);
  } finally {
    await browser.close();
  }
}

// Run if called directly
const currentFile = fileURLToPath(import.meta.url);
const executedFile = process.argv[1];

if (currentFile === executedFile) {
  diagnoseLoadingIssues();
}

export { diagnoseLoadingIssues };
