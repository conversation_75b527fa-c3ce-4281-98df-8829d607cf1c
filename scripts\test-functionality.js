/**
 * Functionality Test Script
 * Tests all enhanced Neo-Brutalism features
 */

import { chromium } from 'playwright';

async function testPortfolioFunctionality() {
  console.log('🧪 Starting comprehensive functionality tests...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 }
  });
  
  const page = await context.newPage();
  
  try {
    // Navigate to the site
    console.log('📍 Navigating to localhost:3000...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    
    // Test 1: Check if all sections are present
    console.log('✅ Test 1: Checking section presence...');
    const sections = await page.$$eval('section', sections => 
      sections.map(section => section.id || section.className)
    );
    console.log('Found sections:', sections);
    
    // Test 2: Test button interactions and sound
    console.log('✅ Test 2: Testing button interactions...');
    const buttons = await page.$$('.brutal-button');
    console.log(`Found ${buttons.length} brutal buttons`);
    
    if (buttons.length > 0) {
      await buttons[0].hover();
      await page.waitForTimeout(500);
      await buttons[0].click();
      await page.waitForTimeout(500);
    }
    
    // Test 3: Test scroll animations
    console.log('✅ Test 3: Testing scroll animations...');
    await page.evaluate(() => {
      window.scrollTo({ top: 500, behavior: 'smooth' });
    });
    await page.waitForTimeout(1000);
    
    await page.evaluate(() => {
      window.scrollTo({ top: 1000, behavior: 'smooth' });
    });
    await page.waitForTimeout(1000);
    
    // Test 4: Test portfolio images
    console.log('✅ Test 4: Checking portfolio images...');
    const portfolioImages = await page.$$('.portfolio-image img');
    console.log(`Found ${portfolioImages.length} portfolio images`);
    
    for (let i = 0; i < portfolioImages.length; i++) {
      const isVisible = await portfolioImages[i].isVisible();
      const src = await portfolioImages[i].getAttribute('src');
      console.log(`Image ${i + 1}: ${isVisible ? 'visible' : 'hidden'}, src: ${src}`);
    }
    
    // Test 5: Test contact form
    console.log('✅ Test 5: Testing contact form...');
    await page.evaluate(() => {
      const contactSection = document.querySelector('#contact');
      if (contactSection) {
        contactSection.scrollIntoView({ behavior: 'smooth' });
      }
    });
    await page.waitForTimeout(1000);
    
    const nameInput = await page.$('input[name="name"]');
    const emailInput = await page.$('input[name="email"]');
    const messageInput = await page.$('textarea[name="message"]');
    
    if (nameInput && emailInput && messageInput) {
      await nameInput.fill('Test User');
      await emailInput.fill('<EMAIL>');
      await messageInput.fill('This is a test message for the enhanced Neo-Brutalism portfolio.');
      
      console.log('Form filled successfully');
    }
    
    // Test 6: Test responsive design
    console.log('✅ Test 6: Testing responsive design...');
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(1000);
    
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(1000);
    
    // Test 7: Test card hover effects
    console.log('✅ Test 7: Testing card hover effects...');
    const cards = await page.$$('.brutal-card');
    console.log(`Found ${cards.length} brutal cards`);
    
    if (cards.length > 0) {
      await cards[0].hover();
      await page.waitForTimeout(500);
    }
    
    // Test 8: Performance check
    console.log('✅ Test 8: Performance metrics...');
    const performanceMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0];
      return {
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime,
        firstContentfulPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-contentful-paint')?.startTime
      };
    });
    
    console.log('Performance metrics:', performanceMetrics);
    
    console.log('🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
  }
}

// Run tests if called directly
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const currentFile = __filename;
const executedFile = process.argv[1];

if (currentFile === executedFile) {
  testPortfolioFunctionality();
}

export { testPortfolioFunctionality };
