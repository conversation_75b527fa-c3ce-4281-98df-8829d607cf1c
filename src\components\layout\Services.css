/**
 * Services Section Styles
 * Neo-Brutalism service cards with proper spacing
 */

.services {
  padding: var(--space-32) 0;
  background-color: var(--color-bg-primary);
  position: relative;
}

.services::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(180deg, var(--color-bg-secondary) 0%, transparent 100%);
  z-index: 1;
}

.services-header {
  text-align: center;
  margin-bottom: var(--space-20);
  position: relative;
  z-index: 2;
}

.section-title {
  font-family: var(--font-display);
  font-weight: var(--font-weight-black);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-tight);
  margin-bottom: var(--space-4);
}

.title-label {
  display: block;
  font-size: var(--font-size-2xl);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-2);
}

.title-main {
  display: block;
  font-size: clamp(3rem, 6vw, 6rem);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
  position: relative;
}

.title-main::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 6px;
  background-color: var(--color-accent);
  box-shadow: var(--shadow-brutal-sm) var(--color-shadow);
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  position: relative;
  z-index: 2;
}

.service-card {
  background-color: var(--color-bg-primary);
  border: 3px solid var(--color-border);
  padding: var(--space-8);
  position: relative;
  transition: var(--transition-base);
  cursor: pointer;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--accent-color);
  transform: translate(8px, 8px);
  z-index: -1;
  transition: var(--transition-base);
}

.service-card:hover {
  transform: translate(4px, 4px);
}

.service-card:hover::before {
  transform: translate(4px, 4px);
}

.service-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-6);
}

.service-title {
  font-family: var(--font-display);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  line-height: var(--line-height-tight);
  flex: 1;
}

.service-icon {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-brutal-sm) var(--color-shadow);
  flex-shrink: 0;
}

.service-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-6);
}

.service-features {
  margin-bottom: var(--space-8);
}

.service-feature {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-3);
}

.feature-bullet {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.service-btn {
  width: 100%;
  padding: var(--space-4) var(--space-6);
  font-family: var(--font-display);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  background-color: transparent;
  border: 2px solid var(--color-border);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  cursor: pointer;
  transition: var(--transition-base);
  position: relative;
}

.service-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--accent-color);
  transform: scaleX(0);
  transform-origin: left;
  transition: var(--transition-base);
  z-index: -1;
}

.service-btn:hover::before {
  transform: scaleX(1);
}

.service-btn:hover {
  color: var(--color-secondary);
  border-color: var(--accent-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .services {
    padding: var(--space-24) 0;
  }
  
  .services-header {
    margin-bottom: var(--space-16);
  }
  
  .services-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .service-card {
    padding: var(--space-6);
  }
  
  .service-card::before {
    transform: translate(6px, 6px);
  }
  
  .service-card:hover {
    transform: translate(3px, 3px);
  }
  
  .service-card:hover::before {
    transform: translate(3px, 3px);
  }
}

@media (max-width: 480px) {
  .services-grid {
    grid-template-columns: 1fr;
  }
  
  .service-card {
    padding: var(--space-5);
  }
  
  .service-title {
    font-size: var(--font-size-lg);
  }
}
