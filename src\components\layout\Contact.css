/**
 * Contact Section Styles
 * Neo-Brutalism contact form and info with proper spacing
 */

.contact {
  padding: var(--space-32) 0;
  background-color: var(--color-bg-primary);
  position: relative;
}

.contact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--color-border) 50%, transparent 100%);
}

.contact-header {
  text-align: center;
  margin-bottom: var(--space-20);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: var(--space-16);
  align-items: start;
}

/* Contact Form */
.contact-form-wrapper {
  background-color: var(--color-bg-secondary);
  border: 3px solid var(--color-border);
  padding: var(--space-10);
  position: relative;
}

.contact-form-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-accent);
  transform: translate(12px, 12px);
  z-index: -1;
}

.contact-form-header {
  margin-bottom: var(--space-8);
}

.contact-form-header h3 {
  font-family: var(--font-display);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  margin-bottom: var(--space-4);
}

.contact-form-header p {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-group label {
  font-family: var(--font-display);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: var(--space-4);
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  border: 2px solid var(--color-border);
  transition: var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: var(--shadow-brutal-sm) var(--color-accent);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.contact-submit {
  margin-top: var(--space-4);
  align-self: flex-start;
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.contact-card {
  background-color: var(--color-neon-green);
  color: var(--color-primary);
  padding: var(--space-8);
  border: 3px solid var(--color-primary);
  box-shadow: var(--shadow-brutal-lg) var(--color-primary);
  transform: rotate(-2deg);
}

.contact-card h4 {
  font-family: var(--font-display);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  margin-bottom: var(--space-4);
}

.contact-card p {
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.contact-item {
  padding: var(--space-6);
  background-color: var(--color-bg-secondary);
  border: 2px solid var(--color-border);
  box-shadow: var(--shadow-brutal-md) var(--color-shadow);
}

.contact-item h5 {
  font-family: var(--font-display);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  margin-bottom: var(--space-2);
}

.contact-item a {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  text-decoration: none;
  transition: var(--transition-fast);
}

.contact-item a:hover {
  color: var(--color-accent);
}

.contact-item p {
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  line-height: var(--line-height-relaxed);
}

.contact-social {
  padding: var(--space-6);
  background-color: var(--color-primary);
  color: var(--color-secondary);
  border: 2px solid var(--color-primary);
  box-shadow: var(--shadow-brutal-md) var(--color-gray-400);
}

.contact-social h5 {
  font-family: var(--font-display);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  margin-bottom: var(--space-4);
}

.social-links {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.social-link {
  font-family: var(--font-display);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-secondary);
  text-decoration: none;
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  transition: var(--transition-fast);
  position: relative;
}

.social-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-accent);
  transition: var(--transition-fast);
}

.social-link:hover::after {
  width: 100%;
}

.social-link:hover {
  color: var(--color-accent);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }
  
  .contact-info {
    order: -1;
  }
}

@media (max-width: 768px) {
  .contact {
    padding: var(--space-24) 0;
  }
  
  .contact-header {
    margin-bottom: var(--space-16);
  }
  
  .contact-content {
    gap: var(--space-10);
  }
  
  .contact-form-wrapper {
    padding: var(--space-8);
  }
  
  .contact-form-wrapper::before {
    transform: translate(8px, 8px);
  }
  
  .contact-card {
    transform: rotate(-1deg);
    padding: var(--space-6);
  }
  
  .contact-item {
    padding: var(--space-5);
  }
  
  .contact-social {
    padding: var(--space-5);
  }
}

@media (max-width: 480px) {
  .contact-form-wrapper {
    padding: var(--space-6);
  }
  
  .brutal-btn-lg {
    width: 100%;
    text-align: center;
  }
  
  .social-links {
    flex-direction: column;
    gap: var(--space-2);
  }
}
