import './Services.css';

const Services = () => {
  const services = [
    {
      title: 'WEB DEVELOPMENT',
      description: 'Custom websites and web applications built with modern technologies and best practices.',
      features: ['React & Next.js', 'Node.js Backend', 'Database Design', 'API Integration'],
    },
    {
      title: 'SOCIAL MEDIA MANAGEMENT',
      description: 'Strategic social media campaigns that engage your audience and grow your brand.',
      features: ['Content Strategy', 'Community Management', 'Analytics & Reporting', 'Paid Advertising'],
    },
    {
      title: 'DIGITAL MARKETING',
      description: 'Data-driven marketing strategies that deliver measurable results and ROI.',
      features: ['SEO Optimization', 'PPC Campaigns', 'Email Marketing', 'Conversion Optimization'],
    },
  ];

  return (
    <section id="services" className="services">
      <div className="container">
        <div className="services-header">
          <h2 className="section-title">
            <span className="title-label">WHAT WE</span>
            <span className="title-main">BUILD</span>
          </h2>
        </div>

        <div className="services-grid">
          {services.map((service, index) => (
            <div key={index} className="service-card">
              <div className="service-header">
                <h3 className="service-title">{service.title}</h3>
              </div>

              <p className="service-description">{service.description}</p>

              <ul className="service-features">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="service-feature">
                    <span className="feature-bullet"></span>
                    {feature}
                  </li>
                ))}
              </ul>

              <button className="service-btn">
                LEARN MORE
              </button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

  useGSAP(() => {
    // Title animation
    gsap.fromTo(titleRef.current, 
      { opacity: 0, y: 60 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      }
    );

    // Services cards animation
    const cards = servicesRef.current.querySelectorAll('.service-card');
    gsap.fromTo(cards,
      { opacity: 0, y: 80, scale: 0.9 },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        ease: 'power2.out',
        stagger: 0.15,
        scrollTrigger: {
          trigger: servicesRef.current,
          start: 'top 70%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      }
    );

  }, { scope: sectionRef });

  return (
    <section ref={sectionRef} id="services" className="services">
      <div className="container">
        <div className="services-header">
          <ScrollReveal animation="fadeUp" delay={0.2}>
            <h2 ref={titleRef} className="section-title">
              <span className="title-label">WHAT WE</span>
              <span className="title-main">BUILD</span>
            </h2>
          </ScrollReveal>
        </div>
        
        <div ref={servicesRef} className="services-grid">
          {services.map((service, index) => (
            <ScrollReveal
              key={index}
              animation="scale"
              delay={index * 0.1}
              className="service-card-wrapper"
            >
              <BrutalCard
                variant="default"
                accentColor={service.color}
                hover={true}
                tilt={true}
                className="service-card"
              >
                <div className="service-header">
                  <h3 className="service-title">{service.title}</h3>
                  <div className="service-icon" style={{ backgroundColor: service.color }}></div>
                </div>

                <p className="service-description">{service.description}</p>

                <ul className="service-features">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="service-feature">
                      <span className="feature-bullet" style={{ backgroundColor: service.color }}></span>
                      {feature}
                    </li>
                  ))}
                </ul>

                <BrutalButton
                  variant="outline"
                  size="md"
                  className="service-btn"
                  onClick={() => {
                    // Handle learn more action
                  }}
                >
                  LEARN MORE
                </BrutalButton>
              </BrutalCard>
            </ScrollReveal>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;
