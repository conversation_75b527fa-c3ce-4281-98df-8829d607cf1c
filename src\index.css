/**
 * Main Stylesheet for Tera Works Portfolio
 * Neo-Brutalism design system implementation
 */

/* Import our design system */
@import './styles/reset.css';
@import './styles/theme.css';

/* Google Fonts - Inter for clean, modern typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Base styles */
body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  min-height: 100vh;
}

/* App container */
#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Global link styles */
a {
  color: var(--color-text-primary);
  transition: var(--transition-fast);
}

a:hover {
  color: var(--color-accent);
}

/* Button base styles */
button {
  font-family: var(--font-primary);
  cursor: pointer;
  transition: var(--transition-base);
}

/* Selection styles */
::selection {
  background-color: var(--color-accent);
  color: var(--color-secondary);
}

::-moz-selection {
  background-color: var(--color-accent);
  color: var(--color-secondary);
}
