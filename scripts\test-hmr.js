import { chromium } from 'playwright';
import fs from 'fs';

async function testHMR() {
  console.log('🔥 Testing Hot Module Replacement (HMR)...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Track WebSocket connections
  let wsConnected = false;
  let wsErrors = [];
  
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('WebSocket')) {
      if (text.includes('connected') || text.includes('open')) {
        wsConnected = true;
        console.log('✅ WebSocket connected:', text);
      } else if (text.includes('failed') || text.includes('error')) {
        wsErrors.push(text);
        console.log('❌ WebSocket error:', text);
      }
    }
    if (msg.type() === 'error' && text.includes('WebSocket')) {
      wsErrors.push(text);
    }
  });
  
  try {
    console.log('🌐 Loading site...');
    await page.goto('http://localhost:3000/', { waitUntil: 'networkidle' });
    await page.waitForTimeout(3000);
    
    // Get initial page content
    console.log('📄 Getting initial page content...');
    const initialTitle = await page.textContent('.hero-title');
    console.log('Initial hero title:', initialTitle?.substring(0, 50) + '...');
    
    // Make a small change to test HMR
    console.log('✏️ Making a test change to Hero component...');
    const heroPath = 'src/components/layout/Hero.jsx';
    const originalContent = fs.readFileSync(heroPath, 'utf8');
    
    // Add a test comment to trigger HMR
    const modifiedContent = originalContent.replace(
      'TERA WORKS',
      'TERA WORKS [HMR TEST]'
    );
    
    fs.writeFileSync(heroPath, modifiedContent);
    console.log('📝 Test change applied to Hero.jsx');
    
    // Wait for HMR to update
    console.log('⏳ Waiting for HMR update...');
    await page.waitForTimeout(3000);
    
    // Check if content updated
    const updatedTitle = await page.textContent('.hero-title');
    console.log('Updated hero title:', updatedTitle?.substring(0, 50) + '...');
    
    const hmrWorking = updatedTitle?.includes('[HMR TEST]');
    console.log(hmrWorking ? '✅ HMR is working!' : '❌ HMR not working');
    
    // Restore original content
    console.log('🔄 Restoring original content...');
    fs.writeFileSync(heroPath, originalContent);
    await page.waitForTimeout(2000);
    
    // Final check
    const restoredTitle = await page.textContent('.hero-title');
    const restored = !restoredTitle?.includes('[HMR TEST]');
    console.log(restored ? '✅ Content restored successfully' : '❌ Content not restored');
    
    // Test results
    console.log('\n🔥 HMR TEST RESULTS:');
    console.log('===================');
    console.log(`🔌 WebSocket connected: ${wsConnected ? 'YES' : 'NO'}`);
    console.log(`❌ WebSocket errors: ${wsErrors.length}`);
    console.log(`🔄 HMR functioning: ${hmrWorking ? 'YES' : 'NO'}`);
    console.log(`✅ Content restored: ${restored ? 'YES' : 'NO'}`);
    
    if (wsErrors.length > 0) {
      console.log('\n❌ WebSocket Errors:');
      wsErrors.forEach(error => console.log(`  - ${error}`));
    }
    
    if (wsErrors.length === 0 && hmrWorking) {
      console.log('\n🎉 HMR IS FULLY FUNCTIONAL!');
      console.log('✅ WebSocket connections stable');
      console.log('✅ Hot reloading working');
      console.log('✅ Development experience optimized');
    } else {
      console.log('\n⚠️ HMR issues detected - but site still functional');
    }
    
  } catch (error) {
    console.error('❌ HMR test failed:', error.message);
  } finally {
    await browser.close();
  }
}

testHMR().catch(console.error);
