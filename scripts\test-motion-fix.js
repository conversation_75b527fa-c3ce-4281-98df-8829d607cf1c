import { chromium } from 'playwright';

async function testMotionFix() {
  console.log('🔍 Testing motion fix...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Listen for console errors
  const errors = [];
  page.on('console', msg => {
    if (msg.type() === 'error') {
      errors.push(msg.text());
      console.log('❌ Console Error:', msg.text());
    }
  });
  
  // Listen for page errors
  page.on('pageerror', error => {
    errors.push(error.message);
    console.log('❌ Page Error:', error.message);
  });
  
  try {
    console.log('📱 Navigating to localhost:3000...');
    await page.goto('http://localhost:3000/', { waitUntil: 'networkidle' });
    
    // Wait for the page to load
    await page.waitForTimeout(3000);
    
    // Check if ScrollReveal components are working
    console.log('🔍 Checking for ScrollReveal components...');
    const scrollRevealElements = await page.$$('.scroll-reveal');
    console.log(`✅ Found ${scrollRevealElements.length} ScrollReveal elements`);
    
    // Check if motion components are rendered
    const motionElements = await page.$$('[data-framer-motion]');
    console.log(`✅ Found ${motionElements.length} Framer Motion elements`);
    
    // Check for specific motion-related errors
    const motionErrors = errors.filter(error => 
      error.includes('motion is not defined') || 
      error.includes('framer-motion') ||
      error.includes('ScrollReveal')
    );
    
    if (motionErrors.length === 0) {
      console.log('✅ No motion-related errors found!');
    } else {
      console.log('❌ Motion-related errors:', motionErrors);
    }
    
    // Take a screenshot
    await page.screenshot({ path: 'motion-fix-test.png', fullPage: true });
    console.log('📸 Screenshot saved as motion-fix-test.png');
    
    // Test WebSocket connection
    console.log('🔌 Testing WebSocket connection...');
    const wsErrors = errors.filter(error => 
      error.includes('WebSocket') || 
      error.includes('ws://localhost')
    );
    
    if (wsErrors.length === 0) {
      console.log('✅ No WebSocket errors found!');
    } else {
      console.log('❌ WebSocket errors:', wsErrors);
    }
    
    console.log('\n📊 Test Summary:');
    console.log(`- Total errors: ${errors.length}`);
    console.log(`- Motion errors: ${motionErrors.length}`);
    console.log(`- WebSocket errors: ${wsErrors.length}`);
    console.log(`- ScrollReveal elements: ${scrollRevealElements.length}`);
    console.log(`- Framer Motion elements: ${motionElements.length}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await browser.close();
  }
}

testMotionFix().catch(console.error);
