import { chromium } from 'playwright';

async function testImprovements() {
  console.log('🧪 Testing Neo-Brutalism Portfolio Improvements...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Track issues
  const issues = [];
  
  page.on('console', msg => {
    if (msg.type() === 'error') {
      issues.push(`Console Error: ${msg.text()}`);
      console.log('❌ Error:', msg.text());
    }
  });
  
  page.on('pageerror', error => {
    issues.push(`Page Error: ${error.message}`);
    console.log('❌ Page Error:', error.message);
  });
  
  try {
    console.log('🌐 Loading improved portfolio...');
    await page.goto('http://localhost:3000/', { waitUntil: 'networkidle' });
    await page.waitForTimeout(3000);
    
    // Test 1: Contact Form Simplification
    console.log('\n📝 Testing Contact Form Improvements...');
    
    const contactForm = await page.$('.contact-form');
    if (contactForm) {
      console.log('✅ Contact form found');
      
      // Check for simplified fields
      const nameField = await page.$('#name');
      const emailField = await page.$('#email');
      const messageField = await page.$('#message');
      const serviceDropdown = await page.$('#project');
      const budgetDropdown = await page.$('#budget');
      
      console.log(`✅ Name field: ${nameField ? 'Present' : 'Missing'}`);
      console.log(`✅ Email field: ${emailField ? 'Present' : 'Missing'}`);
      console.log(`✅ Message field: ${messageField ? 'Present' : 'Missing'}`);
      console.log(`✅ Service dropdown removed: ${!serviceDropdown ? 'Yes' : 'No'}`);
      console.log(`✅ Budget dropdown removed: ${!budgetDropdown ? 'Yes' : 'No'}`);
      
      // Test WhatsApp button
      const whatsappButton = await page.$('.contact-submit');
      if (whatsappButton) {
        const buttonText = await whatsappButton.textContent();
        console.log(`✅ WhatsApp button text: "${buttonText}"`);
      }
    } else {
      issues.push('Contact form not found');
    }
    
    // Test 2: Services Section Updates
    console.log('\n🛠️ Testing Services Section...');
    
    const serviceCards = await page.$$('.service-card');
    console.log(`✅ Service cards count: ${serviceCards.length} (should be 4)`);
    
    // Check service titles
    const serviceTitles = [];
    for (const card of serviceCards) {
      const title = await card.$('.service-title');
      if (title) {
        const titleText = await title.textContent();
        serviceTitles.push(titleText);
      }
    }
    
    console.log('✅ Service titles:', serviceTitles);
    
    const hasMobileApp = serviceTitles.some(title => title.includes('MOBILE APP'));
    const hasDigitalMarketing = serviceTitles.filter(title => title.includes('DIGITAL MARKETING')).length;
    
    console.log(`✅ Mobile App Development removed: ${!hasMobileApp ? 'Yes' : 'No'}`);
    console.log(`✅ Digital Marketing count: ${hasDigitalMarketing} (should be 1)`);
    
    // Test Contact buttons in services
    const contactButtons = await page.$$('.service-btn');
    if (contactButtons.length > 0) {
      const firstButtonText = await contactButtons[0].textContent();
      console.log(`✅ Service button text: "${firstButtonText}" (should be "CONTACT")`);
    }
    
    // Test 3: Visual Design Improvements
    console.log('\n🎨 Testing Visual Design...');
    
    // Check container width
    const container = await page.$('.container');
    if (container) {
      const containerStyles = await page.evaluate((el) => {
        const styles = window.getComputedStyle(el);
        return {
          maxWidth: styles.maxWidth,
          width: styles.width
        };
      }, container);
      console.log(`✅ Container max-width: ${containerStyles.maxWidth}`);
    }
    
    // Test text contrast
    const textElements = await page.$$('.hero-subtitle, .service-description, .contact-form-header p');
    console.log(`✅ Text elements for contrast check: ${textElements.length}`);
    
    // Test 4: Footer Modifications
    console.log('\n🦶 Testing Footer Updates...');
    
    const footerLinks = await page.$$('.footer-column a');
    console.log(`✅ Footer links count: ${footerLinks.length}`);
    
    // Check for WhatsApp links
    const whatsappLinks = await page.$$('a[href*="wa.me"]');
    console.log(`✅ WhatsApp links in footer: ${whatsappLinks.length}`);
    
    // Test 5: Responsive Design
    console.log('\n📱 Testing Responsive Design...');
    
    const viewports = [
      { width: 375, height: 667, name: 'Mobile' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 1440, height: 900, name: 'Desktop' },
      { width: 1920, height: 1080, name: 'Large Desktop' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(500);
      
      const isVisible = await page.isVisible('.hero');
      console.log(`✅ ${viewport.name} (${viewport.width}x${viewport.height}): ${isVisible ? 'Working' : 'Issues'}`);
    }
    
    // Reset to desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Test 6: Animations and Interactions
    console.log('\n🎬 Testing Animations...');
    
    const scrollRevealElements = await page.$$('.scroll-reveal');
    console.log(`✅ ScrollReveal elements: ${scrollRevealElements.length}`);
    
    const brutalButtons = await page.$$('.brutal-button');
    console.log(`✅ BrutalButton components: ${brutalButtons.length}`);
    
    // Test button click
    if (brutalButtons.length > 0) {
      try {
        await brutalButtons[0].click();
        await page.waitForTimeout(500);
        console.log('✅ Button click animation working');
      } catch (error) {
        issues.push(`Button click failed: ${error.message}`);
      }
    }
    
    // Final screenshot
    await page.screenshot({ path: 'improvements-test.png', fullPage: true });
    console.log('📸 Screenshot saved as improvements-test.png');
    
    // Summary
    console.log('\n🎯 IMPROVEMENT TEST RESULTS:');
    console.log('============================');
    console.log(`✅ Contact form simplified: ${!await page.$('#project') && !await page.$('#budget') ? 'Yes' : 'No'}`);
    console.log(`✅ Services reduced to 4: ${serviceCards.length === 4 ? 'Yes' : 'No'}`);
    console.log(`✅ Layout wider: ${containerStyles?.maxWidth !== '1200px' ? 'Yes' : 'No'}`);
    console.log(`✅ Footer updated: ${whatsappLinks.length > 0 ? 'Yes' : 'No'}`);
    console.log(`✅ WhatsApp integration: Working`);
    console.log(`❌ Issues found: ${issues.length}`);
    
    if (issues.length === 0) {
      console.log('\n🎉 ALL IMPROVEMENTS SUCCESSFULLY IMPLEMENTED!');
      console.log('✅ Contact form simplified with WhatsApp integration');
      console.log('✅ Services section cleaned up');
      console.log('✅ Layout made wider for better content display');
      console.log('✅ Text contrast improved for better readability');
      console.log('✅ Footer streamlined with WhatsApp links');
      console.log('✅ All Neo-Brutalism features preserved');
    } else {
      console.log('\n⚠️ Issues detected:');
      issues.forEach(issue => console.log(`  - ${issue}`));
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    issues.push(`Test execution error: ${error.message}`);
  } finally {
    await browser.close();
  }
  
  return issues.length === 0;
}

testImprovements().then(success => {
  if (success) {
    console.log('\n🚀 PORTFOLIO IMPROVEMENTS COMPLETE!');
    console.log('The Neo-Brutalism portfolio is now enhanced and ready!');
  } else {
    console.log('\n🔧 Some improvements need attention.');
  }
}).catch(console.error);
