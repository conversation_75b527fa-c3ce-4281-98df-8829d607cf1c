import { useRef } from 'react';
import { gsap, useGSAP } from '@/utils/gsap';

function App() {
  const containerRef = useRef();
  const titleRef = useRef();
  const subtitleRef = useRef();

  useGSAP(() => {
    // Initial setup - hide elements
    gsap.set([titleRef.current, subtitleRef.current], {
      opacity: 0,
      y: 60,
    });

    // Create entrance animation timeline
    const tl = gsap.timeline();

    tl.to(titleRef.current, {
      opacity: 1,
      y: 0,
      duration: 1,
      ease: 'power3.out',
    })
    .to(subtitleRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: 'power2.out',
    }, '-=0.5');

  }, { scope: containerRef });

  return (
    <div ref={containerRef} className="app">
      <main className="hero">
        <div className="container">
          <h1 ref={titleRef} className="text-display-1 hero-title">
            TERA WORKS
          </h1>
          <p ref={subtitleRef} className="text-h4 hero-subtitle">
            Neo-Brutalism Portfolio
          </p>
        </div>
      </main>
    </div>
  );
}

export default App;
