import { chromium } from 'playwright';

async function finalImprovementsTest() {
  console.log('🎯 Final Comprehensive Test of All Improvements...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  const results = {
    contactForm: false,
    services: false,
    layout: false,
    footer: false,
    whatsapp: false,
    animations: false,
    responsive: false
  };
  
  try {
    console.log('🌐 Loading portfolio...');
    await page.goto('http://localhost:3000/', { waitUntil: 'networkidle' });
    await page.waitForTimeout(3000);
    
    // 1. Contact Form Test
    console.log('\n📝 Testing Contact Form Simplification...');
    const nameField = await page.$('#name');
    const emailField = await page.$('#email');
    const messageField = await page.$('#message');
    const serviceDropdown = await page.$('#project');
    const budgetDropdown = await page.$('#budget');
    const whatsappBtn = await page.$('.contact-submit');
    
    if (nameField && emailField && messageField && !serviceDropdown && !budgetDropdown && whatsappBtn) {
      results.contactForm = true;
      console.log('✅ Contact form simplified successfully');
      
      // Test WhatsApp functionality
      const btnText = await whatsappBtn.textContent();
      if (btnText.includes('WHATSAPP')) {
        results.whatsapp = true;
        console.log('✅ WhatsApp integration working');
      }
    }
    
    // 2. Services Section Test
    console.log('\n🛠️ Testing Services Section...');
    const serviceCards = await page.$$('.service-card');
    const serviceTitles = [];
    
    for (const card of serviceCards) {
      const title = await card.$('.service-title');
      if (title) {
        const titleText = await title.textContent();
        serviceTitles.push(titleText);
      }
    }
    
    const expectedServices = ['WEB DEVELOPMENT', 'SOCIAL MEDIA MANAGEMENT', 'BRAND OPTIMIZATION', 'DIGITAL MARKETING'];
    const hasCorrectServices = serviceCards.length === 4 && 
                              expectedServices.every(service => serviceTitles.includes(service)) &&
                              !serviceTitles.some(title => title.includes('MOBILE APP'));
    
    if (hasCorrectServices) {
      results.services = true;
      console.log('✅ Services section updated correctly');
    }
    
    // Test Contact buttons
    const contactButtons = await page.$$('.service-btn');
    let contactButtonsWork = true;
    for (const btn of contactButtons) {
      const text = await btn.textContent();
      if (!text.includes('CONTACT')) {
        contactButtonsWork = false;
        break;
      }
    }
    
    if (contactButtonsWork) {
      console.log('✅ Service buttons changed to "CONTACT"');
    }
    
    // 3. Layout Width Test
    console.log('\n🎨 Testing Layout Improvements...');
    const container = await page.$('.container');
    if (container) {
      const maxWidth = await page.evaluate((el) => {
        return window.getComputedStyle(el).maxWidth;
      }, container);
      
      if (maxWidth === '1600px') {
        results.layout = true;
        console.log('✅ Layout made wider (1600px)');
      }
    }
    
    // 4. Footer Test
    console.log('\n🦶 Testing Footer Updates...');
    const whatsappLinks = await page.$$('a[href*="wa.me"]');
    const footerColumns = await page.$$('.footer-column');
    
    if (whatsappLinks.length >= 2 && footerColumns.length === 3) {
      results.footer = true;
      console.log('✅ Footer updated with WhatsApp links');
    }
    
    // 5. Animation Test
    console.log('\n🎬 Testing Animations...');
    const scrollRevealElements = await page.$$('.scroll-reveal');
    const brutalButtons = await page.$$('.brutal-button');
    
    if (scrollRevealElements.length > 15 && brutalButtons.length > 15) {
      results.animations = true;
      console.log('✅ All animations preserved');
    }
    
    // 6. Responsive Test
    console.log('\n📱 Testing Responsive Design...');
    const viewports = [
      { width: 375, height: 667 },
      { width: 768, height: 1024 },
      { width: 1440, height: 900 }
    ];
    
    let responsiveWorking = true;
    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(500);
      
      const heroVisible = await page.isVisible('.hero');
      if (!heroVisible) {
        responsiveWorking = false;
        break;
      }
    }
    
    if (responsiveWorking) {
      results.responsive = true;
      console.log('✅ Responsive design working');
    }
    
    // Reset viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Test WhatsApp functionality
    console.log('\n📱 Testing WhatsApp Integration...');
    try {
      // Fill out contact form
      await page.fill('#name', 'Test User');
      await page.fill('#email', '<EMAIL>');
      await page.fill('#message', 'This is a test message for the improved contact form.');
      
      console.log('✅ Contact form can be filled');
      
      // Test service contact button
      const firstServiceBtn = await page.$('.service-btn');
      if (firstServiceBtn) {
        console.log('✅ Service contact buttons ready');
      }
      
    } catch (error) {
      console.log('⚠️ WhatsApp test had issues:', error.message);
    }
    
    // Final screenshot
    await page.screenshot({ path: 'final-improvements.png', fullPage: true });
    console.log('📸 Final screenshot saved');
    
    // Results Summary
    console.log('\n🎯 FINAL IMPROVEMENT RESULTS:');
    console.log('==============================');
    console.log(`📝 Contact Form Simplified: ${results.contactForm ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`🛠️ Services Section Updated: ${results.services ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`🎨 Layout Made Wider: ${results.layout ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`🦶 Footer Streamlined: ${results.footer ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`📱 WhatsApp Integration: ${results.whatsapp ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`🎬 Animations Preserved: ${results.animations ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`📱 Responsive Design: ${results.responsive ? '✅ PASS' : '❌ FAIL'}`);
    
    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n📊 Overall Score: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('\n🎉 ALL IMPROVEMENTS SUCCESSFULLY IMPLEMENTED!');
      console.log('🚀 The Neo-Brutalism portfolio is now enhanced and ready!');
      console.log('\n✨ Key Improvements:');
      console.log('  • Contact form simplified to Name, Email, Message only');
      console.log('  • WhatsApp integration for instant communication');
      console.log('  • Services reduced to 4 core offerings');
      console.log('  • Layout made wider (1600px) for better content display');
      console.log('  • Text contrast improved for better readability');
      console.log('  • Footer streamlined with direct WhatsApp links');
      console.log('  • All Neo-Brutalism animations and effects preserved');
      console.log('  • Responsive design maintained across all devices');
    } else {
      console.log('\n⚠️ Some improvements may need attention');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await browser.close();
  }
  
  return results;
}

finalImprovementsTest().catch(console.error);
