import './Hero.css';

const Hero = () => {
  return (
    <section className="hero">
      <div className="hero-bg"></div>
      <div className="container">
        <div className="hero-content">
          <div className="hero-main">
            <h1 className="hero-title">
              <span className="title-line">TERA WORKS</span>
              <span className="title-line title-accent">DIGITAL</span>
              <span className="title-line">AGENCY</span>
              <span className="title-line title-small">FOR THE FUTURE</span>
            </h1>

            <p className="hero-subtitle">
              We build premium digital experiences that push boundaries and deliver results.
            </p>

            <p className="hero-description">
              From cutting-edge web development to innovative digital solutions,
              we transform your vision into reality with bold design and flawless execution.
            </p>

            <div className="hero-cta">
              <a href="#work" className="brutal-btn brutal-btn-primary brutal-btn-lg">
                🚀 VIEW OUR WORK
              </a>
              <a href="#contact" className="brutal-btn brutal-btn-secondary brutal-btn-lg">
                💬 START PROJECT
              </a>
            </div>
          </div>

          <div className="hero-stats">
            <div className="stat-card">
              <div className="stat-item">
                <div className="stat-number">50+</div>
                <div className="stat-label">Projects Delivered</div>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-item">
                <div className="stat-number">100%</div>
                <div className="stat-label">Client Satisfaction</div>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-item">
                <div className="stat-number">3+</div>
                <div className="stat-label">Years Experience</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );

export default Hero;
