import { useRef } from 'react';
import { gsap, useGSAP } from '@/utils/gsap';
import BrutalButton from '@/components/ui/BrutalButton';
import BrutalCard from '@/components/ui/BrutalCard';
import ScrollReveal from '@/components/animations/ScrollReveal';
import './Hero.css';

const Hero = () => {
  const heroRef = useRef();
  const titleRef = useRef();
  const subtitleRef = useRef();
  const descriptionRef = useRef();
  const ctaRef = useRef();
  const statsRef = useRef();

  useGSAP(() => {
    // Initial setup
    gsap.set([titleRef.current, subtitleRef.current, descriptionRef.current, ctaRef.current, statsRef.current], {
      opacity: 0,
      y: 60,
    });

    // Create entrance animation timeline
    const tl = gsap.timeline({ delay: 0.5 });
    
    tl.to(titleRef.current, {
      opacity: 1,
      y: 0,
      duration: 1.2,
      ease: 'power3.out',
    })
    .to(subtitleRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: 'power2.out',
    }, '-=0.6')
    .to(descriptionRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: 'power2.out',
    }, '-=0.4')
    .to(ctaRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: 'power2.out',
    }, '-=0.4')
    .to(statsRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: 'power2.out',
    }, '-=0.4');

  }, { scope: heroRef });

  return (
    <section ref={heroRef} className="hero">
      <div className="hero-bg"></div>
      <div className="container">
        <div className="hero-content">
          <div className="hero-main">
            <ScrollReveal animation="fadeUp" delay={0.2}>
              <h1 ref={titleRef} className="hero-title">
                <span className="title-line">TERA WORKS</span>
                <span className="title-line title-accent">DIGITAL</span>
                <span className="title-line">AGENCY</span>
                <span className="title-line title-small">FOR THE FUTURE</span>
              </h1>
            </ScrollReveal>

            <ScrollReveal animation="fadeUp" delay={0.4}>
              <p ref={subtitleRef} className="hero-subtitle">
                We build premium digital experiences that push boundaries and deliver results.
              </p>
            </ScrollReveal>

            <ScrollReveal animation="fadeUp" delay={0.6}>
              <p ref={descriptionRef} className="hero-description">
                From cutting-edge web development to innovative digital solutions,
                we transform your vision into reality with bold design and flawless execution.
              </p>
            </ScrollReveal>

            <ScrollReveal animation="bounce" delay={0.8}>
              <div ref={ctaRef} className="hero-cta">
                <BrutalButton
                  href="#work"
                  variant="primary"
                  size="lg"
                  icon="🚀"
                >
                  VIEW OUR WORK
                </BrutalButton>
                <BrutalButton
                  href="#contact"
                  variant="secondary"
                  size="lg"
                  icon="💬"
                >
                  START PROJECT
                </BrutalButton>
              </div>
            </ScrollReveal>
          </div>
          
          <ScrollReveal animation="stagger" stagger={0.2} delay={1.0}>
            <div ref={statsRef} className="hero-stats">
              <BrutalCard variant="primary" accentColor="var(--color-neon-green)" glow>
                <div className="stat-item">
                  <div className="stat-number">50+</div>
                  <div className="stat-label">Projects Delivered</div>
                </div>
              </BrutalCard>
              <BrutalCard variant="primary" accentColor="var(--color-hot-pink)" glow>
                <div className="stat-item">
                  <div className="stat-number">100%</div>
                  <div className="stat-label">Client Satisfaction</div>
                </div>
              </BrutalCard>
              <BrutalCard variant="primary" accentColor="var(--color-cyber-yellow)" glow>
                <div className="stat-item">
                  <div className="stat-number">3+</div>
                  <div className="stat-label">Years Experience</div>
                </div>
              </BrutalCard>
            </div>
          </ScrollReveal>
        </div>
      </div>
    </section>
  );
};

export default Hero;
