/**
 * App Component Styles
 * Neo-Brutalism hero section
 */

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.hero {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-20) 0;
  background-color: var(--color-bg-primary);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 49%, var(--color-gray-100) 49%, var(--color-gray-100) 51%, transparent 51%),
    linear-gradient(-45deg, transparent 49%, var(--color-gray-100) 49%, var(--color-gray-100) 51%, transparent 51%);
  background-size: 20px 20px;
  opacity: 0.3;
  z-index: 1;
}

.hero .container {
  position: relative;
  z-index: 2;
  text-align: center;
}

.hero-title {
  color: var(--color-text-primary);
  margin-bottom: var(--space-6);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-tighter);
  position: relative;
  display: inline-block;
}

.hero-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 8px;
  background-color: var(--color-accent);
  box-shadow: var(--shadow-brutal-md) var(--color-shadow);
}

.hero-subtitle {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  position: relative;
  display: inline-block;
  padding: var(--space-4) var(--space-8);
  background-color: var(--color-bg-secondary);
  border: 3px solid var(--color-border);
  box-shadow: var(--shadow-brutal-lg) var(--color-shadow);
  transform: rotate(-2deg);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero {
    padding: var(--space-16) 0;
  }

  .hero-title::after {
    height: 6px;
  }

  .hero-subtitle {
    padding: var(--space-3) var(--space-6);
    transform: rotate(-1deg);
  }
}

@media (max-width: 480px) {
  .hero {
    padding: var(--space-12) 0;
  }

  .hero-subtitle {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-lg);
  }
}
